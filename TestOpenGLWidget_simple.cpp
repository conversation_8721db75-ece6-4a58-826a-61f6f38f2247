//
// Created by 25724 on 2025/4/3.
//

#include "TestOpenGLWidget.h"
#include <cmath>
#include <QMouseEvent>
#include <QSizePolicy>
#include <fstream>
#include <sstream>
#include <string>
#include <QFile>

TestOpenGLWidget::TestOpenGLWidget(QWidget *parent) : QOpenGLWidget(parent)
{
    // 初始化基本变换参数
    rotationX = 0.0f;
    rotationY = 0.0f;
    scale = 1.0f;
    translateX = 0.0f;
    translateY = 0.0f;
    
    // 设置焦点策略，使部件能接收按键事件
    setFocusPolicy(Qt::StrongFocus);
    
    // 初始化视图参数
    fov = 45.0f;
    nearPlane = 0.1f;
    farPlane = 100.0f;
    cameraDistance = 3.0f;
    
    // 设置窗口大小
    setMinimumSize(800, 600);
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    
    // 初始化模型信息
    modelCenter = QVector3D(0.0f, 0.0f, 0.0f);
    modelScale = 1.0f;
}

TestOpenGLWidget::~TestOpenGLWidget()
{
    makeCurrent();
    glDeleteVertexArrays(1, &VAO_id);
    glDeleteBuffers(1, &VBO_id);
    glDeleteBuffers(1, &EBO_id);
    glDeleteProgram(shaderProgram1_id);
    doneCurrent();
}

// 加载OBJ模型文件
bool TestOpenGLWidget::loadOBJModel(const QString &filePath)
{
    std::ifstream file(filePath.toStdString());
    if (!file.is_open()) {
        qDebug() << "无法打开OBJ文件: " << filePath;
        return false;
    }

    // 临时存储顶点数据
    std::vector<QVector3D> positions;
    std::vector<QVector3D> normals;

    // 清空现有数据
    modelVertices.clear();
    modelIndices.clear();

    // 计算边界盒，用于居中和缩放
    float minX = std::numeric_limits<float>::max();
    float minY = std::numeric_limits<float>::max();
    float minZ = std::numeric_limits<float>::max();
    float maxX = std::numeric_limits<float>::min();
    float maxY = std::numeric_limits<float>::min();
    float maxZ = std::numeric_limits<float>::min();

    std::string line;
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        std::string type;
        iss >> type;

        if (type == "v") { // 顶点位置
            float x, y, z;
            iss >> x >> y >> z;
            positions.push_back(QVector3D(x, y, z));
            
            // 更新边界盒
            minX = std::min(minX, x);
            minY = std::min(minY, y);
            minZ = std::min(minZ, z);
            maxX = std::max(maxX, x);
            maxY = std::max(maxY, y);
            maxZ = std::max(maxZ, z);
        } 
        else if (type == "vn") { // 法线
            float nx, ny, nz;
            iss >> nx >> ny >> nz;
            normals.push_back(QVector3D(nx, ny, nz));
        } 
        else if (type == "f") { // 面
            std::string vertex;
            std::vector<int> posIndices;

            while (iss >> vertex) {
                std::replace(vertex.begin(), vertex.end(), '/', ' ');
                std::istringstream viss(vertex);
                int posIdx;
                
                viss >> posIdx;
                posIndices.push_back(posIdx - 1); // OBJ索引从1开始，需要转换为从0开始
            }

            // 三角化面（假设是凸多边形）
            for (size_t i = 1; i < posIndices.size() - 1; i++) {
                modelIndices.push_back(posIndices[0]);
                modelIndices.push_back(posIndices[i]);
                modelIndices.push_back(posIndices[i + 1]);
            }
        }
    }

    // 计算模型中心和大小
    modelCenter = QVector3D((minX + maxX) * 0.5f, (minY + maxY) * 0.5f, (minZ + maxZ) * 0.5f);
    float modelSize = std::max({maxX - minX, maxY - minY, maxZ - minZ});
    modelScale = 2.0f / modelSize; // 缩放使模型归一化到[-1, 1]范围

    // 将所有顶点数据组装为单一数组
    for (size_t i = 0; i < positions.size(); i++) {
        // 顶点位置（居中和缩放）
        QVector3D pos = (positions[i] - modelCenter) * modelScale;
        modelVertices.push_back(pos.x());
        modelVertices.push_back(pos.y());
        modelVertices.push_back(pos.z());

        // 顶点法线（如果存在）
        QVector3D normal;
        if (i < normals.size()) {
            normal = normals[i].normalized();
        } else {
            // 如果没有法线，使用位置的归一化作为法线
            normal = pos.normalized();
        }
        modelVertices.push_back(normal.x());
        modelVertices.push_back(normal.y());
        modelVertices.push_back(normal.z());
    }

    qDebug() << "OBJ模型加载成功，顶点数:" << positions.size() << "面数:" << modelIndices.size() / 3;
    return true;
}

void TestOpenGLWidget::initializeGL()
{
    initializeOpenGLFunctions();
    
    // 启用深度测试
    glEnable(GL_DEPTH_TEST);
    
    // 尝试加载OBJ模型
    QString objPath = "D:\\MW3DSurfaceReconstruction\\mesh20250331.obj";
    if (QFile::exists(objPath)) {
        qDebug() << "尝试加载OBJ模型...";
        if (!loadOBJModel(objPath)) {
            qDebug() << "OBJ模型加载失败";
            return;
        }
    } else {
        qDebug() << "OBJ文件不存在: " << objPath;
        return;
    }
    
    // 创建VAO和VBO
    glGenVertexArrays(1, &VAO_id);
    glGenBuffers(1, &VBO_id);
    glGenBuffers(1, &EBO_id);
    
    // 绑定VAO
    glBindVertexArray(VAO_id);
    
    // 绑定并设置VBO
    glBindBuffer(GL_ARRAY_BUFFER, VBO_id);
    glBufferData(GL_ARRAY_BUFFER, modelVertices.size() * sizeof(float), modelVertices.data(), GL_STATIC_DRAW);
    
    // 绑定并设置EBO
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, EBO_id);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, modelIndices.size() * sizeof(unsigned int), modelIndices.data(), GL_STATIC_DRAW);
    
    // 设置顶点属性指针 - 位置
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(0);
    
    // 设置顶点属性指针 - 法线
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void*)(3 * sizeof(float)));
    glEnableVertexAttribArray(1);
    
    // 解绑VAO
    glBindVertexArray(0);

    // 从文件加载着色器源代码
    vertexShaderSource = ShaderLoader::loadShaderFromFile("shaders/sphere.vert");
    fragmentShaderSource = ShaderLoader::loadShaderFromFile("shaders/sphere.frag");

    // 检查着色器是否成功加载
    if (vertexShaderSource.isEmpty() || fragmentShaderSource.isEmpty()) {
        qDebug() << "着色器文件加载失败！";
        return;
    }

    // 编译着色器
    unsigned int vertexShader_id = glCreateShader(GL_VERTEX_SHADER);
    QByteArray vertexShaderBytes = vertexShaderSource.toUtf8();
    const char* vertexShaderCStr = vertexShaderBytes.constData();
    glShaderSource(vertexShader_id, 1, &vertexShaderCStr, NULL);
    glCompileShader(vertexShader_id);

    unsigned int fragmentShader_id = glCreateShader(GL_FRAGMENT_SHADER);
    QByteArray fragmentShaderBytes = fragmentShaderSource.toUtf8();
    const char* fragmentShaderCStr = fragmentShaderBytes.constData();
    glShaderSource(fragmentShader_id, 1, &fragmentShaderCStr, NULL);
    glCompileShader(fragmentShader_id);
    
    // 创建着色器程序
    shaderProgram1_id = glCreateProgram();
    glAttachShader(shaderProgram1_id, vertexShader_id);
    glAttachShader(shaderProgram1_id, fragmentShader_id);
    glLinkProgram(shaderProgram1_id);
    
    // 删除着色器
    glDeleteShader(vertexShader_id);
    glDeleteShader(fragmentShader_id);
}

void TestOpenGLWidget::resizeGL(int w, int h)
{
    glViewport(0, 0, w, h);
}

void TestOpenGLWidget::paintGL()
{
    glClearColor(0.1f, 0.1f, 0.1f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    // 创建模型矩阵
    QMatrix4x4 modelMatrix;
    modelMatrix.setToIdentity();
    modelMatrix.scale(scale);
    modelMatrix.rotate(rotationX, 0.0f, 1.0f, 0.0f);
    modelMatrix.rotate(rotationY, 1.0f, 0.0f, 0.0f);

    // 设置MVP矩阵
    float aspect = width() / static_cast<float>(height());

    // 视图矩阵
    QMatrix4x4 viewMatrix;
    viewMatrix.setToIdentity();
    viewMatrix.translate(translateX, translateY, -cameraDistance);

    // 透视投影矩阵
    QMatrix4x4 projectionMatrix;
    projectionMatrix.perspective(fov, aspect, nearPlane, farPlane);

    // 使用着色器程序
    glUseProgram(shaderProgram1_id);

    // 传递矩阵到着色器
    glUniformMatrix4fv(glGetUniformLocation(shaderProgram1_id, "model"), 1, GL_FALSE, modelMatrix.constData());
    glUniformMatrix4fv(glGetUniformLocation(shaderProgram1_id, "view"), 1, GL_FALSE, viewMatrix.constData());
    glUniformMatrix4fv(glGetUniformLocation(shaderProgram1_id, "projection"), 1, GL_FALSE, projectionMatrix.constData());

    // 绑定VAO并绘制
    glBindVertexArray(VAO_id);
    glDrawElements(GL_TRIANGLES, modelIndices.size(), GL_UNSIGNED_INT, 0);
}

// 鼠标事件处理
void TestOpenGLWidget::mousePressEvent(QMouseEvent *event)
{
    lastPos = event->pos();
}

void TestOpenGLWidget::mouseMoveEvent(QMouseEvent *event)
{
    int dx = event->x() - lastPos.x();
    int dy = event->y() - lastPos.y();

    if (event->buttons() & Qt::LeftButton) {
        // 左键拖动 - 旋转
        rotationX += dx * 0.5f;
        rotationY += dy * 0.5f;
    } else if (event->buttons() & Qt::RightButton) {
        // 右键拖动 - 平移
        translateX += dx * 0.01f;
        translateY -= dy * 0.01f;
    }

    lastPos = event->pos();
    update();
}

// 滚轮事件处理
void TestOpenGLWidget::wheelEvent(QWheelEvent *event)
{
    float delta = event->angleDelta().y() / 120.0f;
    scale *= (1.0f + 0.1f * delta);
    scale = qBound(0.1f, scale, 10.0f);
    update();
}

// 键盘事件处理
void TestOpenGLWidget::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
        case Qt::Key_Home:
            // 重置视图
            rotationX = 0.0f;
            rotationY = 0.0f;
            translateX = 0.0f;
            translateY = 0.0f;
            scale = 1.0f;
            qDebug() << "视图已重置";
            break;
    }
    update();
}
