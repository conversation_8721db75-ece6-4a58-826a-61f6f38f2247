//
// Created by 25724 on 2025/4/3.
//

#ifndef UNTITLED13_TESTOPENGLWIDGET_H_BAK
#define UNTITLED13_TESTOPENGLWIDGET_H_BAK

#include <QOpenGLWidget>
#include <QOpenGLFunctions_3_3_Core>
#include <vector>
#include <QMatrix4x4>
#include <QVector3D>
#include <QPoint>
#include <QKeyEvent>
#include <QWheelEvent>
#include "ShaderLoader.h"

class TestOpenGLWidget : public QOpenGLWidget, protected QOpenGLFunctions_3_3_Core
{
Q_OBJECT
public:
    explicit TestOpenGLWidget(QWidget *parent = nullptr);
    ~TestOpenGLWidget();

    // OpenGL对象
    unsigned int VAO_id, VBO_id, EBO_id;
    unsigned int shaderProgram1_id;

protected:
    void initializeGL() override;
    void resizeGL(int w, int h) override;
    void paintGL() override;
    
    // 鼠标事件处理
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    
    // 键盘事件处理
    void keyPressEvent(QKeyEvent *event) override;
    
    // 鼠标滚轮事件
    void wheelEvent(QWheelEvent *event) override;

private:
    // 模型数据
    std::vector<float> modelVertices;
    std::vector<unsigned int> modelIndices;

    // 基本变换参数
    float rotationX, rotationY;
    float scale;
    float translateX, translateY;

    // 鼠标控制
    QPoint lastPos;

    // 视图参数
    float fov;
    float nearPlane;
    float farPlane;
    float cameraDistance;

    // 模型信息
    QVector3D modelCenter;
    float modelScale;

    // 加载OBJ模型文件
    bool loadOBJModel(const QString &filePath);

    // 着色器源代码
    QString vertexShaderSource;
    QString fragmentShaderSource;

signals:

};


#endif //UNTITLED13_TESTOPENGLWIDGET_H_BAK
