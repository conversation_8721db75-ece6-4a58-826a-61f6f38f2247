#version 330 core
in vec3 fragPos;
in vec3 normal;
out vec4 FragColor;

uniform vec3 lightPos;
uniform vec3 viewPos;
uniform float ambientStrength;
uniform float diffuseStrength;
uniform float specularStrength;

void main()
{
    // 基本的白色/灰色材质
    vec3 objectColor = vec3(0.8, 0.8, 0.8);
    
    // 环境光
    vec3 ambient = ambientStrength * objectColor;
    
    // 漫反射
    vec3 norm = normalize(normal);
    vec3 lightDir = normalize(lightPos - fragPos);
    float diff = max(dot(norm, lightDir), 0.0);
    vec3 diffuse = diffuseStrength * diff * objectColor;
    
    // 镜面反射
    vec3 viewDir = normalize(viewPos - fragPos);
    vec3 reflectDir = reflect(-lightDir, norm);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32);
    vec3 specular = specularStrength * spec * vec3(1.0, 1.0, 1.0);
    
    // 组合光照效果
    vec3 result = ambient + diffuse + specular;
    
    FragColor = vec4(result, 1.0);
}
